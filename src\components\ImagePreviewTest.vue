<template>
  <div class="image-preview-test">
    <h2>图片预览旋转同步测试</h2>
    
    <!-- 图片列表 -->
    <div class="image-list">
      <div 
        v-for="(item, index) in imageList" 
        :key="item.id"
        class="image-item"
      >
        <div class="image-wrapper" :style="getImageStyle(item)">
          <img 
            :src="getOriginalUrl(item.url)" 
            @click="openPreview(index)"
            class="image-thumbnail"
          />
        </div>
        <div class="image-controls">
          <button @click="rotateImage(item)" class="rotate-btn">旋转</button>
          <span class="rotation-info">{{ item.rotation }}°</span>
        </div>
      </div>
    </div>

    <!-- 自定义预览弹窗 -->
    <div v-if="previewVisible" class="preview-overlay" @click="closePreview">
      <div class="preview-dialog" @click.stop>
        <div class="preview-header">
          <h3>图片预览</h3>
          <button @click="closePreview" class="close-btn">×</button>
        </div>
        
        <div class="preview-toolbar">
          <button @click="previewRotateLeft">左转</button>
          <button @click="previewRotateRight">右转</button>
          <button @click="previewZoomIn">放大</button>
          <button @click="previewZoomOut">缩小</button>
          <button @click="previewReset">重置</button>
          
          <div class="preview-nav">
            <button @click="previewPrev" :disabled="currentIndex === 0">上一张</button>
            <span>{{ currentIndex + 1 }} / {{ imageList.length }}</span>
            <button @click="previewNext" :disabled="currentIndex === imageList.length - 1">下一张</button>
          </div>
        </div>
        
        <div class="preview-content">
          <img 
            :src="getOriginalUrl(currentImage.url)"
            :style="previewImageStyle"
            class="preview-image"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ImagePreviewTest',
  data() {
    return {
      imageList: [
        {
          id: '1',
          url: 'https://via.placeholder.com/400x300/FF6B6B/FFFFFF?text=Image+1',
          rotation: 0
        },
        {
          id: '2', 
          url: 'https://via.placeholder.com/400x300/4ECDC4/FFFFFF?text=Image+2',
          rotation: 0
        },
        {
          id: '3',
          url: 'https://via.placeholder.com/400x300/45B7D1/FFFFFF?text=Image+3', 
          rotation: 0
        }
      ],
      previewVisible: false,
      currentIndex: 0,
      previewRotation: 0,
      previewScale: 1
    };
  },
  computed: {
    currentImage() {
      return this.imageList[this.currentIndex] || {};
    },
    previewImageStyle() {
      return {
        transform: `rotate(${this.previewRotation}deg) scale(${this.previewScale})`,
        transition: 'transform 0.3s ease',
        maxWidth: '90%',
        maxHeight: '90%'
      };
    }
  },
  methods: {
    getOriginalUrl(url) {
      return url.split('?')[0];
    },
    
    getImageStyle(item) {
      return {
        transform: `rotate(${item.rotation}deg)`,
        transition: 'transform 0.3s ease'
      };
    },
    
    rotateImage(item) {
      const index = this.imageList.findIndex(img => img.id === item.id);
      if (index !== -1) {
        this.imageList[index].rotation = (this.imageList[index].rotation + 90) % 360;
      }
    },
    
    openPreview(index) {
      this.currentIndex = index;
      this.previewVisible = true;
      this.previewRotation = this.imageList[index].rotation;
      this.previewScale = 1;
    },
    
    closePreview() {
      // 同步旋转状态
      const normalizedRotation = ((this.previewRotation % 360) + 360) % 360;
      const standardRotation = Math.round(normalizedRotation / 90) * 90;
      this.imageList[this.currentIndex].rotation = standardRotation;
      
      this.previewVisible = false;
      this.previewRotation = 0;
      this.previewScale = 1;
    },
    
    previewRotateLeft() {
      this.previewRotation -= 90;
    },
    
    previewRotateRight() {
      this.previewRotation += 90;
    },
    
    previewZoomIn() {
      this.previewScale = Math.min(this.previewScale * 1.2, 3);
    },
    
    previewZoomOut() {
      this.previewScale = Math.max(this.previewScale / 1.2, 0.5);
    },
    
    previewReset() {
      this.previewScale = 1;
      this.previewRotation = this.imageList[this.currentIndex].rotation;
    },
    
    previewPrev() {
      if (this.currentIndex > 0) {
        this.syncCurrentRotation();
        this.currentIndex--;
        this.previewRotation = this.imageList[this.currentIndex].rotation;
        this.previewScale = 1;
      }
    },
    
    previewNext() {
      if (this.currentIndex < this.imageList.length - 1) {
        this.syncCurrentRotation();
        this.currentIndex++;
        this.previewRotation = this.imageList[this.currentIndex].rotation;
        this.previewScale = 1;
      }
    },
    
    syncCurrentRotation() {
      const normalizedRotation = ((this.previewRotation % 360) + 360) % 360;
      const standardRotation = Math.round(normalizedRotation / 90) * 90;
      this.imageList[this.currentIndex].rotation = standardRotation;
    }
  }
};
</script>

<style scoped>
.image-preview-test {
  padding: 20px;
}

.image-list {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.image-item {
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 10px;
  background: white;
}

.image-wrapper {
  width: 150px;
  height: 150px;
  overflow: hidden;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-thumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover;
  cursor: pointer;
}

.image-controls {
  margin-top: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.rotate-btn {
  padding: 5px 10px;
  background: #409eff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.rotation-info {
  font-size: 12px;
  color: #666;
}

.preview-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.preview-dialog {
  background: white;
  border-radius: 8px;
  width: 80%;
  height: 80%;
  display: flex;
  flex-direction: column;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #eee;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
}

.preview-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
  border-bottom: 1px solid #eee;
  background: #f5f5f5;
}

.preview-nav {
  display: flex;
  align-items: center;
  gap: 10px;
}

.preview-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f0f0f0;
}

.preview-image {
  max-width: 90%;
  max-height: 90%;
  object-fit: contain;
}

button {
  padding: 5px 10px;
  margin: 0 2px;
  background: #409eff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

button:disabled {
  background: #ccc;
  cursor: not-allowed;
}
</style>
