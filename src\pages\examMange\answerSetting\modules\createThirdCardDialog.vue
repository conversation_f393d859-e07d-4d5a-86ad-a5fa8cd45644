<template>
  <div>
    <el-dialog
      title="答题卡设置"
      :visible.sync="dialogVisible"
      width="40%"
      :before-close="handleClose"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      v-loading.fullscreen.lock="isLoading"
      element-loading-text="客户端启动中"
      element-loading-spinner="el-icon-loading"
      element-loading-background="rgba(0, 0, 0, 0.8)"
    >
      <div class="dialog-body">
        <el-form ref="form" :model="thirdCardInfo" label-width="140px">
          <el-form-item label="尺寸">
            <el-radio-group v-model="thirdCardInfo.size">
              <el-radio label="A3">A3</el-radio>
              <el-radio label="A4">A4</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="页面">
            <el-radio-group v-model="thirdCardInfo.pageType" @change="sendChangePage">
              <el-radio label="1">1面</el-radio>
              <el-radio label="2">2面</el-radio>
              <el-radio label="3">3面</el-radio>
              <el-radio label="4">4面</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="版式" v-if="thirdCardInfo.size == 'A3'">
            <el-radio-group v-model="thirdCardInfo.pageLayout">
              <el-radio :label="IPAGELAYOUT.A3">两栏</el-radio>
              <el-radio :label="IPAGELAYOUT.A33">三栏</el-radio>
              <el-radio :label="IPAGELAYOUT.A32">正三反二</el-radio>
              <el-radio :label="IPAGELAYOUT.A23">正二反三</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="上传或扫描答题卡">
            <span class="tip"> 提示：请上传或扫描未作答的答题卡图片 </span>
            <div class="upload-container">
              <div :class="['img-list',{'more': filePathList.length > 2}]" v-for="(item, index) in filePathList" :key="index">
                <div class="img-wrapper" :style="getImageStyle(item)">
                  <el-image
                    class="img-list-item"
                    :src="getOriginalUrl(item.url)"
                    @click="openCustomPreview(index)"
                    fit="contain">
                  </el-image>
                </div>
                <span class="img-page">第{{ Math.floor(index/2) + 1 }}张{{ index%2 == 0 ? '正' : '反' }}面</span>
                <i
                  class="exchange-icon el-icon-sort"
                  v-if="filePathList.length == 2 && index == 0"
                  @click="handleExchange(item)"
                ></i>
                <i class="rotate-icon el-icon-refresh-right" @click="handleRotate(item)"></i>
                <i class="delete-icon el-icon-delete" @click="handleRemove(item)"></i>
              </div>
              <div v-if="!filePathList.length || showUploadType == 'scan'" @click="startApp" class="upload scan-upload">
                <div class="upload-icon">
                  <div class="icon-scan">
                  </div>
                  <p >扫描答题卡</p>
                </div>
              </div>
              <el-upload
                v-if="!filePathList.length || showUploadType == 'upload'"
                :disabled="disabledUpload"
                ref="uploadFile"
                :action="uploadUrl"
                :accept="coverAccept"
                :show-file-list="false"
                :on-success="uploadSuccess"
                :before-upload="beforeUpload"
                :data="uploadData"
                :http-request="ossUpload"
                type="drag"
                class="upload"
                :class="{ disabledUpload: disabledUpload }"
              >
                <div class="upload-icon">
                  <div class="icon-photo">
                  </div>
                  <p>上传图片</p>
                </div>
              </el-upload>
            </div>
          </el-form-item>
          <el-form-item v-if="filePathList.length > 0">
            <p>注：</p>
            <p>1、请确保答题卡图片方向正确，如需调整，请旋转图片;</p>
            <p>2、请确保答题卡图片依次排列，如需调整，请点击互换按钮对调顺序;</p>
            <p>3、点击图片可查看大图</p>
          </el-form-item>
          <el-form-item v-if="tipType">
            <p v-if="tipType == 1" style="color:red;">检测到尚未安装C30扫描，请<a target="_blank" href="https://fs.iclass30.com/scan/personal-scan.exe">下载</a>安装后再进行扫描</p>
            <p v-else-if="tipType == 2" style="color:red;">扫描仪连接异常</p>
            <p v-else-if="tipType == 3" style="color:red;">检测到纸张</p>
            <p v-else-if="tipType == 4">扫描中...</p>
            <p v-else-if="tipType == 5" style="color:#0f6400">C30扫描客户端已运行</p>
            <p v-else-if="tipType == 6" style="color:#0f6400">扫描仪已连接，点击<el-button type="text" @click="startScan">开始扫描</el-button></p>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="confirmCreate">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 自定义图片预览弹窗 -->
    <el-dialog
      title="图片预览"
      :visible.sync="previewVisible"
      width="80%"
      :before-close="closePreview"
      append-to-body
      class="image-preview-dialog"
    >
      <div class="preview-container">
        <div class="preview-toolbar">
          <el-button-group>
            <el-button @click="previewRotateLeft" icon="el-icon-refresh-left" size="small">左转</el-button>
            <el-button @click="previewRotateRight" icon="el-icon-refresh-right" size="small">右转</el-button>
            <el-button @click="previewZoomIn" icon="el-icon-zoom-in" size="small">放大</el-button>
            <el-button @click="previewZoomOut" icon="el-icon-zoom-out" size="small">缩小</el-button>
            <el-button @click="previewReset" icon="el-icon-refresh" size="small">重置</el-button>
          </el-button-group>
          <div class="preview-nav">
            <el-button @click="previewPrev" :disabled="currentPreviewIndex === 0" size="small">上一张</el-button>
            <span class="preview-counter">{{ currentPreviewIndex + 1 }} / {{ filePathList.length }}</span>
            <el-button @click="previewNext" :disabled="currentPreviewIndex === filePathList.length - 1" size="small">下一张</el-button>
          </div>
        </div>
        <div class="preview-image-container">
          <img
            ref="previewImage"
            :src="getOriginalUrl(currentPreviewItem.url)"
            :style="previewImageStyle"
            @load="onPreviewImageLoad"
          />
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import ossUploadFile from '@/utils/ossUploadFile';
import { guid,get_suffix,generateUUID } from '@/utils/index';
import socket from '@/utils/socket';
import { IPAGELAYOUT } from '@/typings/card';

export default {
  props: {
    dialogVisible: {
      type: Boolean,
      required: true,
    },
  },
  computed: {
    ...mapState(['loginInfo', 'schoolInfo']),
    disabledUpload() {
      return (
        (this.thirdCardInfo.pageType == 1 && this.filePathList.length >= 1) ||
        (this.thirdCardInfo.pageType == 2 && this.filePathList.length >= 2) ||
        (this.thirdCardInfo.pageType == 3 && this.filePathList.length >= 3) ||
        (this.thirdCardInfo.pageType == 4 && this.filePathList.length >= 4)
      );
    },
    // 当前预览的图片项
    currentPreviewItem() {
      return this.filePathList[this.currentPreviewIndex] || {};
    },
    // 预览图片的样式
    previewImageStyle() {
      return {
        transform: `rotate(${this.previewRotation}deg) scale(${this.previewScale})`,
        transition: 'transform 0.3s ease',
        maxWidth: '100%',
        maxHeight: '100%',
        objectFit: 'contain'
      };
    }
  },
  data() {
    return {
      IPAGELAYOUT,
      thirdCardInfo: {
        size: 'A3',
        pageType: '2', //0:双面 1:单面
        pageLayout: IPAGELAYOUT.A3 , //：1 A4单栏  2：A3两栏  3：A3三栏 4:正3反2 5:正2反3
      },
      coverAccept: '.jpg,.png',
      uploadUrl: '', //word上传地址
      uploadData: {}, //word上传所附带的参数
      filePath:'gece_image/thirdCard',
      filePathList: [], //选择的文件上传地址
      srcList: [],
      fsUrl: process.env.VUE_APP_FS_URL, //地址
      roomId: generateUUID().replace(/-/g,''),
      showUploadType:"",
      // 预览相关数据
      previewVisible: false,
      currentPreviewIndex: 0,
      previewRotation: 0, // 当前预览图片的旋转角度
      previewScale: 1, // 当前预览图片的缩放比例
      originalImageSize: { width: 0, height: 0 }, // 原始图片尺寸
      TIPTYPE:{
        UN_START:0,
        UN_INSTALL:1,
        SCAN_CONNECT_FAIL:2,
        NO_PAPER:3,
        SCANING:4,
        APP_START:5,
        SCAN_CONNECT_SUCCESS:6
      },
      tipType:"", //0:未启动 1:未安装 2:扫描仪连接异常 3:未检测到纸张 4:扫描中 5:C30扫描客户端已运行 6:扫描仪连接成功
      decviceCode:"",
      isLoading:false,
    };
  },
  created() {
      // 加入房间
      socket.joinRoom(this.roomId);
      // 监听客户端上线
      socket.addListener('scanClientOnline', this.scanClientOnline);
      // 监听客户端下线
      socket.addListener('scanClientOffline', this.scanClientOffline);
      // 监听扫描仪链接成功
      socket.addListener('deviceConnectioned', this.deviceConnectioned);
      // 监听扫描上传图片
      socket.addListener('scanThirdCardSuccess', this.scanThirdCardSuccess);
      // 监听客户端握手
      // socket.addListener('clientReady', this.clientReady);
      // socket.addListener('clientClose', this.clientClose);
      // this.decviceCode = this.$localSave.get('decviceCode');
      // if (this.decviceCode) {
      //   socket.joinRoom(this.decviceCode);
      //   this.sendShakeHands(this.decviceCode);
      // }
  },
  destroyed(){
    if (this.onLineData) {
      let data = {
        type: 'closeExamHandle',
        data: '{}',
      };
      let msg = {
        type: 'send2room',
        toUser: this.onLineData.decviceCode,
        msg: JSON.stringify(data),
      };
      socket.sendMessage(msg);
    }
  },
  methods: {
    /**
     * @name 启动应用
    */
    startApp(){
      this.setTipType("");
      this.showUploadType = "scan";
      const url = `c30scan://?roomId=${this.roomId}&type=1`;
      window.location.href = url;
      this.isLoading = true;
      let timer = setInterval(() => {
        if (this.onLineData != null) {
          clearInterval(timer);
          timer = null;
          this.isLoading = false;
        }
      }, 1000);
      timer && setTimeout(() => {
        clearInterval(timer);
        if (this.onLineData == null) {
          this.setTipType(this.TIPTYPE.UN_INSTALL);
          this.isLoading = false;
        }
      }, 12000);
    },
    /**
     * @name 开始扫描
    */
    startScan(){
      let data = {
          type: 'startScan',
          data: '',
        };
        let msg = {
          type: 'send2room',
          toUser: this.onLineData.decviceCode,
          msg: JSON.stringify(data),
        };
        socket.sendMessage(msg);
        // this.setTipType(this.TIPTYPE.SCANING);
    },
    sendChangePage(){
      if (!this.onLineData) return;
      let data = {
        type: 'changePage',
        data: JSON.stringify({pageNum: this.thirdCardInfo.pageType}),
      };
      let msg = {
          type: 'send2room',
          toUser: this.onLineData.decviceCode,
          msg: JSON.stringify(data),
        };
      socket.sendMessage(msg);
    },
    // // 发送握手
    // sendShakeHands(decviceCode) {
    //   let roomId = {
    //     roomId: this.roomId,
    //   };
    //   let data = {
    //     type: 'shakeHands',
    //     data: JSON.stringify(roomId),
    //   };
    //   let msg = {
    //     type: 'send2room',
    //     toUser: decviceCode,
    //     msg: JSON.stringify(data),
    //   };
    //   socket.sendMessage(msg);
    // },
    // 监听客户端上线
    scanClientOnline(msg) {
      this.onLineData = JSON.parse(msg.data);
      this.sendChangePage();
      // this.$localSave.set('decviceCode', this.onLineData.decviceCode);
      // socket.joinRoom(this.onLineData.decviceCode);
      // this.setTipType(this.TIPTYPE.APP_START);
    },
    //监听客户端下线
    scanClientOffline(msg) {
      this.onLineData = null;
      this.setTipType("");
    },
    // // 监听扫描仪链接成功
    deviceConnectioned(){
      // this.setTipType(this.TIPTYPE.SCAN_CONNECT_SUCCESS);
    },
    //设置提示类型
    setTipType(type){
      this.tipType = type;
    },
    // // 监听客户端准备就绪
    // clientReady(msg) {
    //   this.readyStatus = msg;
    //   console.log('客户端准备就绪', this.readyStatus);
    //   if (this.readyStatus != null) {
    //     this.reConnectionHandle(this.decviceCode);
    //   }
    // },
    // //客户端关闭
    // clientClose(msg) {
    //   this.closeStatus = msg;
    //   console.log('客户端关闭', this.closeStatus);
    //   if (this.closeStatus != null) {
    //     this.startClient();
    //   }
    // },
    // //启动客户端
    // startClient() {
    //   let data = {
    //     type: 'startClient',
    //     data: JSON.stringify({type:1}),
    //   };
    //   let sendMsg = {
    //     type: 'send2room',
    //     toUser: this.decviceCode,
    //     msg: JSON.stringify(data),
    //   };
    //   socket.sendMessage(sendMsg);
    // },
    // //重新唤起客户端
    // reConnectionHandle(decviceCode) {
    //   let roomId = {
    //     roomId: this.roomId,
    //     type:1
    //   };
    //   let data = {
    //     type: 'reConnectionHandle',
    //     data: JSON.stringify(roomId),
    //   };
    //   let msg = {
    //     type: 'send2room',
    //     toUser: decviceCode,
    //     msg: JSON.stringify(data),
    //   };
    //   socket.sendMessage(msg);
    // },
    // 扫描上传图片
    scanThirdCardSuccess(msg) {
      this.filePathList = [];
      this.srcList = [];
      let data = JSON.parse(msg.data);
      let paths = JSON.parse(data.paths);
      paths.forEach((item) => {
        this.filePathList.push({
          id: generateUUID(),
          url: item,
          rotation: 0, // 添加旋转角度属性
        });
        this.srcList.push(item);
      })
      // this.setTipType(this.TIPTYPE.APP_START);
    },
    handleClose() {
      this.$emit('close-dialog');
      this.resetData();
    },
    resetData() {
      this.filePathList = [];
      this.thirdCardInfo = {
        size: 'A3',
        pageType: '2', //0:双面 1:单面
        pageLayout: IPAGELAYOUT.A3, //：1 A4单栏  2：A3两栏  3：A3三栏 4:正3反2 5:正2反3
      };
    },
    /**
     * @name: 确定创建三方卡
     */
    confirmCreate() {
      if (this.filePathList.length == 0) {
        this.$message.warning('请上传答题卡');
        return;
      }
      if(this.thirdCardInfo.pageType == 1 && this.filePathList.length > 1){
        this.$message.warning('单面仅支持一张图片');
        return;
      }
      if (this.thirdCardInfo.pageType == 2 && this.filePathList.length != 2) {
        this.$message.warning('请上传两张图片');
        return;
      }
      if (this.thirdCardInfo.pageType == 3 && this.filePathList.length != 3) {
        this.$message.warning('请上传三张图片');
        return;
      }
      if (this.thirdCardInfo.pageType == 4 && this.filePathList.length != 4) {
        this.$message.warning('请上传四张图片');
        return;
      }
      let imgUrl = this.filePathList.map((item, index) => {
        return {
          page: index + 1,
          url: item.url,
        };
      });
      this.$emit('confirm-create', {
        thirdCardInfo: this.thirdCardInfo,
        imgUrl: imgUrl,
      });
      this.resetData();
    },
    /**
     * word上传前
     */
     async beforeUpload(file) {
      let promise = new Promise(async (resolve, reject) => {
        if(file.size > 20*1024*1024 ){
          this.$message.error('文件大小不能超过20MB');
          reject()
          return;
        }
        if(!this.coverAccept.split(',').includes(get_suffix(file.name))){
          this.$message.error('文件格式不支持，请选择'+this.coverAccept+'格式的文档');
          reject()
          return;
        }
        let path = this.getDateForPath();
        await ossUploadFile.getSTSToken(path);
        resolve(true);
        });
      return promise; // 通过返回一个promis对象解决
    },
    ossUpload(data){
      return new Promise((resolve, reject) => {
        let path = this.getDateForPath() + guid() + '/f' + get_suffix(data.file.name);
        ossUploadFile.uploadFile(data.file, path, (res) => {
          if (res.code == 1) {
            resolve(res.res)
          } else {
            this.$message.error('上传失败')
            reject(res)
          }
        })
      });
    },
    /**
     * 上传成功回调
     */
    uploadSuccess(response, file, fileList) {
      this.showUploadType = "upload";
      this.filePathList.push({
        id: generateUUID(),
        url: this.fsUrl + '/' + response.name,
        rotation: 0, // 添加旋转角度属性
      });
      this.srcList.push(this.fsUrl + '/' + response.name);
    },
    /**
     * 根据当前时间当前用户的学校id和用户id拼接文件夹路径
     * @returns {string}
     */
     getDateForPath() {
      let date = new Date()
      let y = date.getFullYear()
      let m = date.getMonth() + 1
      m = m < 10 ? ('0' + m) : m
      let d = date.getDate()
      d = d < 10 ? ('0' + d) : d
      // 当用户学校id不为空时
      return this.filePath + '/' + y + '/' + m + '/' + d + '/' ;
    },
    /**
     * word文件太大的提示
     */
    handleMaxSize(file) {
      this.$Message.error('文件' + file.name + '太大，不允许超过20M');
    },
    /**
     * 上传的文件格式验证失败的提示
     */
    handleFormat(file) {
      this.$Message.error('文件' + file.name + '格式不支持，请选择jpg,png格式的文档');
    },
    /**
     * @name:交换图片顺序
     */
    handleExchange(data) {
      let index = this.filePathList.findIndex(item => item.id == data.id);
      let index2 = this.filePathList.findIndex(item => item.id != data.id);
      this.filePathList[index] = this.filePathList[index2];
      this.filePathList[index2] = data;
      this.srcList = this.filePathList.map(item => {
        return item.url;
      });
    },
    /**
     * @name:旋转图片
     */
    handleRotate(item) {
      const index = this.filePathList.findIndex(data => data.id === item.id);
      if (index !== -1) {
        const currentRotation = this.filePathList[index].rotation || 0;
        const newRotation = (currentRotation + 90) % 360;

        // 更新图片项的旋转角度
        this.$set(this.filePathList, index, {
          ...this.filePathList[index],
          rotation: newRotation
        });

        // 更新URL以包含旋转参数
        this.updateImageUrlWithRotation(this.filePathList[index], newRotation);
      }
    },
    /**
     * @name:删除图片
     */
    handleRemove(data) {
      this.filePathList = this.filePathList.filter(item => item.id != data.id);
      this.srcList = this.filePathList.map(item => {
        return item.url;
      });
    },

    /**
     * @name: 获取原始图片URL（去除旋转参数）
     */
    getOriginalUrl(url) {
      if (!url) return '';
      return url.split('?')[0];
    },

    /**
     * @name: 获取图片列表中的样式（应用旋转）
     */
    getImageStyle(item) {
      const rotation = item.rotation || 0;
      return {
        transform: `rotate(${rotation}deg)`,
        transition: 'transform 0.3s ease',
        width: '100%',
        height: '100%',
        overflow: 'hidden'
      };
    },

    /**
     * @name: 打开自定义预览
     */
    openCustomPreview(index) {
      this.currentPreviewIndex = index;
      this.previewVisible = true;
      this.previewRotation = this.filePathList[index].rotation || 0;
      this.previewScale = 1;
    },

    /**
     * @name: 关闭预览并同步旋转状态
     */
    closePreview() {
      // 同步旋转状态到图片列表
      const currentItem = this.filePathList[this.currentPreviewIndex];
      if (currentItem) {
        // 将预览中的旋转角度标准化为0, 90, 180, 270
        const normalizedRotation = ((this.previewRotation % 360) + 360) % 360;
        const standardRotation = Math.round(normalizedRotation / 90) * 90;

        // 更新图片项的旋转角度
        this.$set(this.filePathList, this.currentPreviewIndex, {
          ...currentItem,
          rotation: standardRotation
        });

        // 更新URL以包含旋转参数（用于后端处理）
        this.updateImageUrlWithRotation(currentItem, standardRotation);
      }

      this.previewVisible = false;
      this.previewRotation = 0;
      this.previewScale = 1;
    },

    /**
     * @name: 更新图片URL包含旋转参数
     */
    updateImageUrlWithRotation(item, rotation) {
      const originalUrl = this.getOriginalUrl(item.url);
      if (rotation === 0) {
        item.url = originalUrl;
      } else {
        item.url = originalUrl + `?x-oss-process=image/rotate,${rotation}`;
      }

      // 更新srcList
      this.srcList = this.filePathList.map(item => item.url);
    },

    /**
     * @name: 预览图片加载完成
     */
    onPreviewImageLoad() {
      // 可以在这里获取图片的原始尺寸
      const img = this.$refs.previewImage;
      if (img) {
        this.originalImageSize = {
          width: img.naturalWidth,
          height: img.naturalHeight
        };
      }
    },

    /**
     * @name: 预览中左转
     */
    previewRotateLeft() {
      this.previewRotation -= 90;
    },

    /**
     * @name: 预览中右转
     */
    previewRotateRight() {
      this.previewRotation += 90;
    },

    /**
     * @name: 预览中放大
     */
    previewZoomIn() {
      this.previewScale = Math.min(this.previewScale * 1.2, 3);
    },

    /**
     * @name: 预览中缩小
     */
    previewZoomOut() {
      this.previewScale = Math.max(this.previewScale / 1.2, 0.5);
    },

    /**
     * @name: 预览重置
     */
    previewReset() {
      this.previewScale = 1;
      this.previewRotation = this.filePathList[this.currentPreviewIndex].rotation || 0;
    },

    /**
     * @name: 预览上一张
     */
    previewPrev() {
      if (this.currentPreviewIndex > 0) {
        // 先同步当前图片的旋转状态
        this.syncCurrentPreviewRotation();
        // 切换到上一张
        this.currentPreviewIndex--;
        this.previewRotation = this.filePathList[this.currentPreviewIndex].rotation || 0;
        this.previewScale = 1;
      }
    },

    /**
     * @name: 预览下一张
     */
    previewNext() {
      if (this.currentPreviewIndex < this.filePathList.length - 1) {
        // 先同步当前图片的旋转状态
        this.syncCurrentPreviewRotation();
        // 切换到下一张
        this.currentPreviewIndex++;
        this.previewRotation = this.filePathList[this.currentPreviewIndex].rotation || 0;
        this.previewScale = 1;
      }
    },

    /**
     * @name: 同步当前预览图片的旋转状态
     */
    syncCurrentPreviewRotation() {
      const currentItem = this.filePathList[this.currentPreviewIndex];
      if (currentItem) {
        const normalizedRotation = ((this.previewRotation % 360) + 360) % 360;
        const standardRotation = Math.round(normalizedRotation / 90) * 90;

        this.$set(this.filePathList, this.currentPreviewIndex, {
          ...currentItem,
          rotation: standardRotation
        });

        this.updateImageUrlWithRotation(currentItem, standardRotation);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.tip {
  color: #fd032f;
}
.upload-container {
  display: flex;
}
.upload {
  width: 100px;
  background: #fff;
    border: 1px dashed #dcdee2;
    border-radius: 4px;
    text-align: center;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: border-color .2s ease;
    margin-right: 10px;
    &:hover{
      border: 1px dashed #2d8cf0;
    }
    .icon-scan{
      height: 32px;
      width: 32px;
      margin: 0 auto;
      background: url("../../../../assets/scan/scan-upload.png");
    }
  .icon-photo{
    height: 32px;
      width: 32px;
      margin: 0 auto;
      background: url("../../../../assets/scan/photo-upload.png");
  }
}
.disabledUpload {
  cursor: not-allowed;
}
.img-list {
  width: 100px;
  height: 100px;
  margin-right: 50px;
  position: relative;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  background: #fff;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
  &.more{
    margin-right: 10px;
  }
  .img-wrapper {
    width: 100%;
    height: 100%;
    overflow: hidden;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .img-list-item {
    width: 100%;
    height: 100%;
    cursor: pointer;
  }
  .img-page {
    position: absolute;
    bottom: -15px;
    height: 20px;
    left: 15px;
  }
  .rotate-icon {
    position: absolute;
    bottom: 0px;
    right: 25px;
    font-size: 20px;
    color: #409eff;
  }
  .delete-icon {
    position: absolute;
    bottom: 0px;
    right: 0;
    font-size: 20px;
    color: red;
  }
  .exchange-icon {
    position: absolute;
    bottom: 50px;
    right: -30px;
    transform: rotate(90deg);
    color: #409eff;
  }
}

.upload-icon {
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: 100px;
  height: 100px;
}
</style>
<style lang="scss">
.disabledUpload {
  .ivu-upload-drag {
    cursor: not-allowed;
  }
  .ivu-upload-drag:hover {
    border: 1px dashed #dcdee2;
  }
}
</style>